'use server';

import db from '@/db';
import {redirect} from 'next/navigation';



export async  function editSnippet(id:number, code:string) {
  console.log('id , code', id, code);
  await db.snippet.update({
    where: { id },
    data: { code },
  });
  redirect(`/snippets/${id}`);
}

export async function deleteSnippet(id: number) {
  await db.snippet.delete({ where: { id } });
  redirect('/');
}


export async function createSnippet(formState:{message:string},formData: FormData) {
  try {
    const title = formData.get('title')?.toString();
    const code = formData.get('code')?.toString();

    if (typeof title !== 'string' || title.length < 3) {
      return {
        message: 'Title is required and must be at least 3 characters',
      };
    }

    if (typeof code !== 'string' || code.length < 10) {
      return {
        message: 'Code is required and must be at least 10 characters',
      };
    }

    if (!title || !code) return;
    const snippet = await db.snippet.create({
      data: {
        title,
        code,
      },
    });
    console.log('snippet', snippet);
    
  } catch (error:unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
      };
    } else {
      return {
        message: 'Something went wrong',
      };
    }
  }
  redirect('/');
  
}